import base64
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

import concurrent.futures
from pathlib import Path

from zhipuai import ZhipuAI
import time
import re
from typing import List, Tuple, Optional
import json

class ImageRecognitionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("电表图片识别系统")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # API配置
        self.api_key = "291d67e8a4ecaed13031ff3656c034cb.sKJwVp58oNjrPz1n"
        self.client = ZhipuAI(api_key=self.api_key)
        self.max_concurrent = 30
        
        # 变量
        self.folder_path = tk.StringVar()
        self.recognition_type = tk.IntVar(value=1)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="准备就绪")
        
        # 结果存储
        self.results = {}
        self.processing = False
        
        self.setup_ui()
    
    def setup_ui(self):
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, text="电表图片识别系统", 
                              font=('微软雅黑', 20, 'bold'), 
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack()
        
        # API密钥设置区域
        api_frame = tk.LabelFrame(self.root, text="API密钥设置",
                                font=('微软雅黑', 12),
                                bg='#f0f0f0', fg='#34495e',
                                padx=10, pady=10)
        api_frame.pack(fill='x', padx=20, pady=10)

        api_entry_frame = tk.Frame(api_frame, bg='#f0f0f0')
        api_entry_frame.pack(fill='x')

        tk.Label(api_entry_frame, text="API密钥:",
                font=('微软雅黑', 10), bg='#f0f0f0').pack(side='left')

        self.api_key_var = tk.StringVar(value=self.api_key)
        self.api_entry = tk.Entry(api_entry_frame, textvariable=self.api_key_var,
                                font=('微软雅黑', 10), width=35, show='*')
        self.api_entry.pack(side='left', fill='x', expand=True, padx=(10, 5))

        # 显示/隐藏密钥按钮
        self.show_key = False
        self.toggle_btn = tk.Button(api_entry_frame, text="👁",
                                  command=self.toggle_key_visibility,
                                  font=('微软雅黑', 10),
                                  bg='#95a5a6', fg='white',
                                  relief='flat', width=3)
        self.toggle_btn.pack(side='left', padx=(0, 5))

        update_key_btn = tk.Button(api_entry_frame, text="更新密钥",
                                 command=self.update_api_key,
                                 font=('微软雅黑', 10),
                                 bg='#e67e22', fg='white',
                                 relief='flat', padx=15)
        update_key_btn.pack(side='right')

        # 文件夹选择区域
        folder_frame = tk.LabelFrame(self.root, text="选择图片文件夹",
                                   font=('微软雅黑', 12),
                                   bg='#f0f0f0', fg='#34495e',
                                   padx=10, pady=10)
        folder_frame.pack(fill='x', padx=20, pady=10)

        folder_entry_frame = tk.Frame(folder_frame, bg='#f0f0f0')
        folder_entry_frame.pack(fill='x')

        self.folder_entry = tk.Entry(folder_entry_frame, textvariable=self.folder_path,
                                   font=('微软雅黑', 10), width=50)
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(folder_entry_frame, text="浏览",
                             command=self.browse_folder,
                             font=('微软雅黑', 10),
                             bg='#3498db', fg='white',
                             relief='flat', padx=20)
        browse_btn.pack(side='right')
        
        # 识别类型选择区域
        type_frame = tk.LabelFrame(self.root, text="选择识别类型", 
                                 font=('微软雅黑', 12), 
                                 bg='#f0f0f0', fg='#34495e',
                                 padx=10, pady=10)
        type_frame.pack(fill='x', padx=20, pady=10)
        
        type1_radio = tk.Radiobutton(type_frame, text="类型1: 电表显示屏和条形码识别", 
                                   variable=self.recognition_type, value=1,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type1_radio.pack(anchor='w', pady=5)
        
        type2_radio = tk.Radiobutton(type_frame, text="类型2: 条形码和资产编码识别", 
                                   variable=self.recognition_type, value=2,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type2_radio.pack(anchor='w', pady=5)
        
        # 类型说明 - 缩小高度
        desc_frame = tk.Frame(type_frame, bg='#f0f0f0')
        desc_frame.pack(fill='x', pady=5)

        desc_text = tk.Text(desc_frame, height=3, font=('微软雅黑', 8),
                           bg='#ecf0f1', fg='#2c3e50', relief='flat',
                           wrap='word', state='disabled')
        desc_text.pack(fill='x')

        self.desc_text = desc_text
        self.update_description()

        # 绑定类型选择变化事件
        self.recognition_type.trace('w', lambda *args: self.update_description())
        
        # 控制按钮区域
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=20, pady=10)

        self.start_btn = tk.Button(control_frame, text="开始识别",
                                 command=self.start_recognition,
                                 font=('微软雅黑', 10, 'bold'),
                                 bg='#27ae60', fg='white',
                                 relief='flat', padx=20, pady=5)
        self.start_btn.pack(side='left', padx=(0, 10))

        self.stop_btn = tk.Button(control_frame, text="停止",
                                command=self.stop_recognition,
                                font=('微软雅黑', 10),
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=20, pady=5,
                                state='disabled')
        self.stop_btn.pack(side='left', padx=(0, 10))

        self.export_btn = tk.Button(control_frame, text="导出到Excel",
                                  command=self.export_to_excel,
                                  font=('微软雅黑', 10, 'bold'),
                                  bg='#f39c12', fg='white',
                                  relief='flat', padx=20, pady=5,
                                  state='disabled')
        self.export_btn.pack(side='left')
        
        # 进度条区域
        progress_frame = tk.LabelFrame(self.root, text="处理进度",
                                     font=('微软雅黑', 12),
                                     bg='#f0f0f0', fg='#34495e',
                                     padx=10, pady=10)
        progress_frame.pack(fill='x', padx=20, pady=20)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(pady=10)

        self.status_label = tk.Label(progress_frame, textvariable=self.status_var,
                                   font=('微软雅黑', 10), bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.pack()
    
    def update_description(self):
        """更新类型描述"""
        self.desc_text.config(state='normal')
        self.desc_text.delete(1.0, tk.END)
        
        if self.recognition_type.get() == 1:
            desc = """类型1说明：
图片分三种情况：
1. 若图中只有一个带有显示屏的数字的电表，识别显示屏的数字以及条形码下面的编号
2. 若图中有两个电表，就只需要读取两个电表条形码下面的编号
3. 若只有一个条形码以06021DY开头的电表，此时不要读取显示屏数字，只读取条形码下面的编号和下面那个小卡片后面的三行编号，三行编号一定要组合起来"""
        else:
            desc = """类型2说明：
读取图片中条形码下面的编号和下面那个卡片的资产编码，资产编码两行一定要组合起来，只返回这两个地方的内容"""
        
        self.desc_text.insert(1.0, desc)
        self.desc_text.config(state='disabled')
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(title="选择图片文件夹")
        if folder:
            self.folder_path.set(folder)

    def update_api_key(self):
        """更新API密钥"""
        new_key = self.api_key_var.get().strip()
        if not new_key:
            messagebox.showerror("错误", "请输入有效的API密钥")
            return

        if new_key == self.api_key:
            messagebox.showinfo("提示", "新密钥与当前密钥相同")
            return

        try:
            # 更新API密钥
            self.api_key = new_key
            self.client = ZhipuAI(api_key=self.api_key)
            messagebox.showinfo("成功", "API密钥已更新！")
            print(f"API密钥已更新: {new_key[:10]}...")  # 只显示前10位用于调试
        except Exception as e:
            messagebox.showerror("错误", f"更新API密钥失败: {str(e)}")
            # 恢复原密钥
            self.api_key_var.set(self.api_key)
    
    def get_prompt_text(self, recognition_type: int) -> str:
        """根据识别类型获取提示文本"""
        if recognition_type == 1:
            return """图片分三种情况，若图中只有一个带有显示屏的数字的电表且条形码下面的编号以54B开头，识别显示屏的数字(会包含小数点，是一个小数)以及条形码下面的编号，若图中有两个电表，就只需要读取两个电表条形码下面的编号，若只有一个条形码以06021DY开头的电表，此时一定不要读取显示屏数字，只读取条形码下面的编号和下面那个小卡片后面的三行编号，不要包含中文，三行编号一定要组合起来且组合的时候中间不要加任何东西，只返回这两个地方的内容，写成一个列表的格式，列表格式如下["", ""]，其他的东西都不要"""
        else:
            return """读取图片中条形码下面的编号(以0602开头的那一行数据)和下面那个卡片的资产编码，资产编码一定有两行，第一行是以06001开头的那个，两行一定要组合起来，组合的时候中间不要加任何东西，只返回这两个地方的内容，写成一个列表的格式，只返回这个列表，列表格式如下["", ""]，其他的东西都不要，不要有中文什么的返回"""

    def get_image_files(self, folder_path: str) -> List[str]:
        """获取文件夹中的图片文件，按文件名排序"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        image_files = []

        try:
            for file_path in Path(folder_path).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    image_files.append(str(file_path))
        except Exception as e:
            messagebox.showerror("错误", f"读取文件夹失败: {str(e)}")
            return []

        # 按文件名排序
        image_files.sort(key=lambda x: Path(x).name)
        return image_files

    def encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """将图片编码为base64"""
        try:
            with open(image_path, 'rb') as img_file:
                return base64.b64encode(img_file.read()).decode('utf-8')
        except Exception as e:
            print(f"编码图片失败 {image_path}: {str(e)}")
            return None

    def process_single_image_sync(self, image_path: str, index: int, prompt_text: str, max_retries: int = 3) -> Tuple[int, str, Optional[str], str]:
        """同步处理单张图片"""
        file_name = Path(image_path).name

        # 编码图片
        img_base64 = self.encode_image_to_base64(image_path)
        if not img_base64:
            return index, file_name, None, "编码失败"

        # 重试机制
        for attempt in range(max_retries):
            try:
                # 调用API
                response = self.client.chat.completions.create(
                    model="glm-4.1v-thinking-flashx",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": img_base64
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": prompt_text
                                }
                            ]
                        }
                    ]
                )

                result = response.choices[0].message.content
                return index, file_name, result, "成功"

            except Exception as e:
                if attempt == max_retries - 1:
                    return index, file_name, None, f"失败: {str(e)}"
                else:
                    # 等待后重试
                    time.sleep(1)

        return index, file_name, None, "重试失败"

    def process_images_concurrent(self, image_files: List[str], prompt_text: str):
        """并发处理所有图片"""
        import concurrent.futures

        total = len(image_files)
        completed = 0

        print(f"开始并发处理 {total} 张图片，最大并发数: {self.max_concurrent}")

        # 使用线程池进行并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, image_path in enumerate(image_files):
                future = executor.submit(self.process_single_image_sync, image_path, i, prompt_text)
                future_to_index[future] = i
                print(f"提交任务 {i+1}: {Path(image_path).name}")

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_index):
                if not self.processing:  # 检查是否被停止
                    print("处理被用户停止")
                    break

                index, file_name, result, status = future.result()
                completed += 1

                print(f"完成任务 {index+1}/{total}: {file_name} - {status}")

                # 存储结果
                self.results[index] = {
                    'file_name': file_name,
                    'result': result,
                    'status': status
                }

                # 更新UI（需要在主线程中执行）
                self.root.after(0, self.update_progress, completed, total, file_name, status)

        print(f"并发处理完成，共处理 {completed} 张图片")

    def update_progress(self, completed: int, total: int, file_name: str, status: str):
        """更新进度条"""
        # 更新进度条
        progress = (completed / total) * 100
        self.progress_var.set(progress)

        # 更新状态
        self.status_var.set(f"处理中... {completed}/{total} - {file_name} - {status}")

        # 如果完成了，启用导出按钮
        if completed == total:
            self.status_var.set(f"处理完成！共处理 {total} 张图片")
            self.export_btn.config(state='normal')
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            self.processing = False

    def start_recognition(self):
        """开始识别"""
        if not self.folder_path.get():
            messagebox.showerror("错误", "请选择图片文件夹")
            return

        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成")
            return

        # 获取图片文件
        image_files = self.get_image_files(self.folder_path.get())
        if not image_files:
            messagebox.showwarning("警告", "文件夹中没有找到图片文件")
            return

        # 清空之前的结果
        self.results.clear()

        # 重置进度
        self.progress_var.set(0)
        self.processing = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.export_btn.config(state='disabled')

        # 获取提示文本
        prompt_text = self.get_prompt_text(self.recognition_type.get())

        # 在新线程中运行并发处理
        def run_concurrent():
            self.process_images_concurrent(image_files, prompt_text)

        thread = threading.Thread(target=run_concurrent, daemon=True)
        thread.start()

    def stop_recognition(self):
        """停止识别"""
        self.processing = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("已停止")



    def clean_data(self, data: str) -> str:
        """清理数据：删除空格和标点符号"""
        if not data:
            return ""

        import re
        # 删除所有空格
        cleaned = re.sub(r'\s+', '', data)
        # 删除常见标点符号（保留字母数字）
        cleaned = re.sub(r'[^\w]', '', cleaned)
        # 删除下划线（\w包含下划线，但我们可能不需要）
        # cleaned = re.sub(r'_', '', cleaned)

        return cleaned.strip()

    def parse_ai_result(self, result_text: str) -> Tuple[str, str]:
        """解析AI返回的列表格式结果"""
        print("=" * 80)
        print("AI返回的原始结果:")
        print(repr(result_text))  # 使用repr显示所有字符，包括换行符等
        print("AI返回的原始结果（可读格式）:")
        print(result_text)
        print("-" * 40)

        if not result_text:
            print("结果为空，返回空字符串")
            return "", ""

        try:
            # 清理文本，移除所有特殊标记和中文注释
            import re

            # 移除所有可能的特殊标记
            cleaned_text = re.sub(r'<\|begin_of_box\|>', '', result_text)
            cleaned_text = re.sub(r'<\|end_of_box\|>', '', cleaned_text)
            cleaned_text = re.sub(r'\[<\|begin_of_box\|>', '[', cleaned_text)
            cleaned_text = re.sub(r'<\|end_of_box\|>\]', ']', cleaned_text)
            cleaned_text = re.sub(r'\[<\|begin_of_box\|><\|end_of_box\|>\]', '[]', cleaned_text)

            # 移除所有中文注释（包括各种格式）
            cleaned_text = re.sub(r'\n（注：.*?）', '', cleaned_text, flags=re.DOTALL)
            cleaned_text = re.sub(r'（注：.*?）', '', cleaned_text, flags=re.DOTALL)
            cleaned_text = re.sub(r'\n\(注：.*?\)', '', cleaned_text, flags=re.DOTALL)
            cleaned_text = re.sub(r'\(注：.*?\)', '', cleaned_text, flags=re.DOTALL)

            # 移除其他可能的中文说明
            cleaned_text = re.sub(r'[\u4e00-\u9fff]+.*', '', cleaned_text)

            cleaned_text = cleaned_text.strip()

            print(f"清理后的文本: {repr(cleaned_text)}")
            print(f"清理后的文本（可读格式）: {cleaned_text}")

            # 修复格式问题：如果缺少开头引号，尝试修复
            if cleaned_text.startswith('[') and not cleaned_text.startswith('["'):
                # 检查是否缺少开头引号
                if '"' in cleaned_text:
                    # 尝试修复格式：在第一个字符后添加引号
                    fixed_text = cleaned_text.replace('[', '["', 1)
                    print(f"尝试修复格式: {repr(fixed_text)}")
                    cleaned_text = fixed_text

            # 尝试使用json解析
            import json
            parsed = json.loads(cleaned_text)
            if isinstance(parsed, list) and len(parsed) >= 2:
                data1 = self.clean_data(str(parsed[0]))
                data2 = self.clean_data(str(parsed[1]))
                print(f"JSON解析成功:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2
        except Exception as e:
            print(f"JSON解析失败: {e}")
            pass

        try:
            # 尝试使用正则表达式解析
            import re

            # 匹配 ["内容1", "内容2"] 格式
            pattern = r'\["([^"]*)",\s*"([^"]*)"\]'
            match = re.search(pattern, result_text)
            if match:
                data1 = self.clean_data(match.group(1))
                data2 = self.clean_data(match.group(2))
                print(f"正则表达式解析成功（双引号格式）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

            # 匹配 ['内容1', '内容2'] 格式
            pattern = r"\['([^']*)',\s*'([^']*)'\]"
            match = re.search(pattern, result_text)
            if match:
                data1 = self.clean_data(match.group(1))
                data2 = self.clean_data(match.group(2))
                print(f"正则表达式解析成功（单引号格式）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

            # 匹配缺少开头引号的格式：[内容1", "内容2]
            pattern = r'\[([^"]*)",\s*"([^"]*)(?:"|]\s*$)'
            match = re.search(pattern, result_text)
            if match:
                data1 = self.clean_data(match.group(1))
                data2 = self.clean_data(match.group(2))
                print(f"正则表达式解析成功（修复缺少引号格式）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

            # 匹配没有引号的格式：[数据1, 数据2] 或 [<|begin_of_box|>数据1, 数据2<|end_of_box|>]
            pattern = r'\[(?:<\|begin_of_box\|>)?([^,\]]+),\s*([^,\]]+?)(?:<\|end_of_box\|>)?\]'
            match = re.search(pattern, result_text)
            if match:
                data1 = self.clean_data(match.group(1))
                data2 = self.clean_data(match.group(2))
                print(f"正则表达式解析成功（无引号格式）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

            # 更宽松的匹配：提取两个被引号包围的内容
            pattern = r'"([^"]+)"[^"]*"([^"]+)"'
            match = re.search(pattern, result_text)
            if match:
                data1 = self.clean_data(match.group(1))
                data2 = self.clean_data(match.group(2))
                print(f"正则表达式解析成功（宽松匹配）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

            # 最宽松的匹配：提取任何以0602开头和06001开头的数据
            pattern1 = r'(0602[A-Z0-9]+)'
            pattern2 = r'(06001[A-Z0-9]+)'
            match1 = re.search(pattern1, result_text)
            match2 = re.search(pattern2, result_text)
            if match1 and match2:
                data1 = self.clean_data(match1.group(1))
                data2 = self.clean_data(match2.group(1))
                print(f"正则表达式解析成功（模式匹配）:")
                print(f"  数据1: '{data1}'")
                print(f"  数据2: '{data2}'")
                print("=" * 80)
                return data1, data2

        except Exception as e:
            print(f"正则解析失败: {e}")
            pass

        # 如果解析失败，返回原始文本和空字符串
        print(f"所有解析方法都失败，返回原始文本")
        print(f"  数据1: '{result_text}'")
        print(f"  数据2: ''")
        print("=" * 80)
        return result_text, ""

    def export_to_excel(self):
        """导出结果到Excel"""
        try:
            print(f"开始导出，结果数量: {len(self.results)}")  # 调试信息

            if not self.results:
                messagebox.showwarning("警告", "没有结果可导出")
                return

            # 准备数据
            data = []

            for i in sorted(self.results.keys()):
                result_data = self.results[i]
                print(f"处理第{i}条数据: {result_data}")  # 调试信息

                # 解析AI返回的列表数据 - 修复状态判断
                if result_data['result'] and result_data['status'] in ['成功', '重试成功']:
                    data1, data2 = self.parse_ai_result(result_data['result'])
                    print(f"解析结果: '{data1}' | '{data2}'")  # 调试信息
                else:
                    data1, data2 = "", ""
                    print(f"无有效数据，状态: {result_data['status']}")  # 调试信息

                # 根据识别类型调整数据顺序
                recognition_type = self.recognition_type.get()
                if recognition_type == 1:
                    # 类型1：调换顺序，第二列数据放第一列，第一列数据放第二列
                    row_data = {
                        '数据1': data2,  # 原来的第二列
                        '数据2': data1   # 原来的第一列
                    }
                    print(f"类型1数据调换后: '{data2}' | '{data1}'")  # 调试信息
                else:
                    # 类型2：保持原顺序
                    row_data = {
                        '数据1': data1,
                        '数据2': data2
                    }
                    print(f"类型2数据保持原顺序: '{data1}' | '{data2}'")  # 调试信息

                data.append(row_data)

            print(f"准备导出 {len(data)} 条记录")  # 调试信息

            # 选择保存位置
            recognition_type = self.recognition_type.get()
            default_name = f"电表识别结果_类型{recognition_type}_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"

            file_path = filedialog.asksaveasfilename(
                title="保存Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                initialfile=default_name
            )

            print(f"选择的文件路径: {file_path}")  # 调试信息

            if file_path:
                try:
                    # 使用openpyxl直接操作Excel
                    from openpyxl import Workbook

                    wb = Workbook()
                    ws = wb.active

                    # 写入表头
                    ws['A1'] = '数据1'
                    ws['B1'] = '数据2'

                    # 写入数据
                    for i, row_data in enumerate(data, start=2):
                        ws[f'A{i}'] = row_data['数据1']
                        ws[f'B{i}'] = row_data['数据2']

                    # 保存文件
                    wb.save(file_path)
                    print("Excel文件保存成功")  # 调试信息
                    messagebox.showinfo("成功", f"结果已导出到: {file_path}\n共导出 {len(data)} 条记录")

                except Exception as e:
                    print(f"保存Excel失败: {str(e)}")  # 调试信息
                    messagebox.showerror("错误", f"导出失败: {str(e)}")
            else:
                print("用户取消了文件保存")  # 调试信息

        except Exception as e:
            print(f"导出过程出错: {str(e)}")  # 调试信息
            messagebox.showerror("错误", f"导出过程出错: {str(e)}")

def main():
    root = tk.Tk()
    ImageRecognitionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
