import base64
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import asyncio
import concurrent.futures
from pathlib import Path
import pandas as pd
from zhipuai import ZhipuAI
import time
import re
from typing import List, Tuple, Optional
import json

class ImageRecognitionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("电表图片识别系统")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # API配置
        self.api_key = "291d67e8a4ecaed13031ff3656c034cb.sKJwVp58oNjrPz1n"
        self.client = ZhipuAI(api_key=self.api_key)
        self.max_concurrent = 30
        
        # 变量
        self.folder_path = tk.StringVar()
        self.recognition_type = tk.IntVar(value=1)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="准备就绪")
        
        # 结果存储
        self.results = {}
        self.processing = False
        
        self.setup_ui()
    
    def setup_ui(self):
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, text="电表图片识别系统", 
                              font=('微软雅黑', 20, 'bold'), 
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack()
        
        # 文件夹选择区域
        folder_frame = tk.LabelFrame(self.root, text="选择图片文件夹", 
                                   font=('微软雅黑', 12), 
                                   bg='#f0f0f0', fg='#34495e',
                                   padx=10, pady=10)
        folder_frame.pack(fill='x', padx=20, pady=10)
        
        folder_entry_frame = tk.Frame(folder_frame, bg='#f0f0f0')
        folder_entry_frame.pack(fill='x')
        
        self.folder_entry = tk.Entry(folder_entry_frame, textvariable=self.folder_path,
                                   font=('微软雅黑', 10), width=50)
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(folder_entry_frame, text="浏览", 
                             command=self.browse_folder,
                             font=('微软雅黑', 10),
                             bg='#3498db', fg='white',
                             relief='flat', padx=20)
        browse_btn.pack(side='right')
        
        # 识别类型选择区域
        type_frame = tk.LabelFrame(self.root, text="选择识别类型", 
                                 font=('微软雅黑', 12), 
                                 bg='#f0f0f0', fg='#34495e',
                                 padx=10, pady=10)
        type_frame.pack(fill='x', padx=20, pady=10)
        
        type1_radio = tk.Radiobutton(type_frame, text="类型1: 电表显示屏和条形码识别", 
                                   variable=self.recognition_type, value=1,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type1_radio.pack(anchor='w', pady=5)
        
        type2_radio = tk.Radiobutton(type_frame, text="类型2: 条形码和资产编码识别", 
                                   variable=self.recognition_type, value=2,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type2_radio.pack(anchor='w', pady=5)
        
        # 类型说明
        desc_frame = tk.Frame(type_frame, bg='#f0f0f0')
        desc_frame.pack(fill='x', pady=10)
        
        desc_text = tk.Text(desc_frame, height=6, font=('微软雅黑', 9),
                           bg='#ecf0f1', fg='#2c3e50', relief='flat',
                           wrap='word', state='disabled')
        desc_text.pack(fill='x')
        
        self.desc_text = desc_text
        self.update_description()
        
        # 绑定类型选择变化事件
        self.recognition_type.trace('w', lambda *args: self.update_description())
        
        # 控制按钮区域
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=20, pady=20)
        
        self.start_btn = tk.Button(control_frame, text="开始识别", 
                                 command=self.start_recognition,
                                 font=('微软雅黑', 12, 'bold'),
                                 bg='#27ae60', fg='white',
                                 relief='flat', padx=30, pady=10)
        self.start_btn.pack(side='left', padx=(0, 10))
        
        self.stop_btn = tk.Button(control_frame, text="停止", 
                                command=self.stop_recognition,
                                font=('微软雅黑', 12),
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=30, pady=10,
                                state='disabled')
        self.stop_btn.pack(side='left')
        
        # 进度条区域
        progress_frame = tk.LabelFrame(self.root, text="处理进度", 
                                     font=('微软雅黑', 12), 
                                     bg='#f0f0f0', fg='#34495e',
                                     padx=10, pady=10)
        progress_frame.pack(fill='x', padx=20, pady=10)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(pady=10)
        
        self.status_label = tk.Label(progress_frame, textvariable=self.status_var,
                                   font=('微软雅黑', 10), bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.pack()
        
        # 结果显示区域
        result_frame = tk.LabelFrame(self.root, text="处理结果", 
                                   font=('微软雅黑', 12), 
                                   bg='#f0f0f0', fg='#34495e',
                                   padx=10, pady=10)
        result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 创建Treeview来显示结果
        columns = ('序号', '文件名', '识别结果', '状态')
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.result_tree.heading(col, text=col)
            if col == '序号':
                self.result_tree.column(col, width=60, anchor='center')
            elif col == '文件名':
                self.result_tree.column(col, width=200)
            elif col == '识别结果':
                self.result_tree.column(col, width=300)
            else:
                self.result_tree.column(col, width=80, anchor='center')
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)
        
        self.result_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 导出按钮
        export_frame = tk.Frame(self.root, bg='#f0f0f0')
        export_frame.pack(fill='x', padx=20, pady=10)
        
        self.export_btn = tk.Button(export_frame, text="导出到Excel", 
                                  command=self.export_to_excel,
                                  font=('微软雅黑', 10),
                                  bg='#f39c12', fg='white',
                                  relief='flat', padx=20, pady=5,
                                  state='disabled')
        self.export_btn.pack(side='right')
    
    def update_description(self):
        """更新类型描述"""
        self.desc_text.config(state='normal')
        self.desc_text.delete(1.0, tk.END)
        
        if self.recognition_type.get() == 1:
            desc = """类型1说明：
图片分三种情况：
1. 若图中只有一个带有显示屏的数字的电表，识别显示屏的数字以及条形码下面的编号
2. 若图中有两个电表，就只需要读取两个电表条形码下面的编号
3. 若只有一个条形码以06021DY开头的电表，此时不要读取显示屏数字，只读取条形码下面的编号和下面那个小卡片后面的三行编号，三行编号一定要组合起来"""
        else:
            desc = """类型2说明：
读取图片中条形码下面的编号和下面那个卡片的资产编码，资产编码两行一定要组合起来，只返回这两个地方的内容"""
        
        self.desc_text.insert(1.0, desc)
        self.desc_text.config(state='disabled')
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(title="选择图片文件夹")
        if folder:
            self.folder_path.set(folder)
    
    def get_prompt_text(self, recognition_type: int) -> str:
        """根据识别类型获取提示文本"""
        if recognition_type == 1:
            return """图片分三种情况，若图中只有一个带有显示屏的数字的电表，识别显示屏的数字以及条形码下面的编号，若图中有两个电表，就只需要读取两个电表条形码下面的编号，若只有一个条形码以06021DY开头的电表，此时不要读取显示屏数字，只读取条形码下面的编号和下面那个小卡片后面的三行编号，三行编号一定要组合起来，只返回这两个地方的内容，写成一个列表的格式，列表格式如下["", ""]，其他的东西都不要"""
        else:
            return """读取图片中条形码下面的编号和下面那个卡片的资产编码，资产编码两行一定要组合起来，只返回这两个地方的内容，写成一个列表的格式，只返回这个列表，列表格式如下["", ""]，其他的东西都不要"""

    def get_image_files(self, folder_path: str) -> List[str]:
        """获取文件夹中的图片文件，按文件名排序"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        image_files = []

        try:
            for file_path in Path(folder_path).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    image_files.append(str(file_path))
        except Exception as e:
            messagebox.showerror("错误", f"读取文件夹失败: {str(e)}")
            return []

        # 按文件名排序
        image_files.sort(key=lambda x: Path(x).name)
        return image_files

    def encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """将图片编码为base64"""
        try:
            with open(image_path, 'rb') as img_file:
                return base64.b64encode(img_file.read()).decode('utf-8')
        except Exception as e:
            print(f"编码图片失败 {image_path}: {str(e)}")
            return None

    async def process_single_image(self, image_path: str, index: int, prompt_text: str,
                                 semaphore: asyncio.Semaphore, max_retries: int = 3) -> Tuple[int, str, Optional[str], str]:
        """处理单张图片"""
        async with semaphore:
            file_name = Path(image_path).name

            # 编码图片
            img_base64 = self.encode_image_to_base64(image_path)
            if not img_base64:
                return index, file_name, None, "编码失败"

            # 重试机制
            for attempt in range(max_retries):
                try:
                    # 调用API
                    response = self.client.chat.completions.create(
                        model="glm-4.1v-thinking-flashx",
                        messages=[
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": img_base64
                                        }
                                    },
                                    {
                                        "type": "text",
                                        "text": prompt_text
                                    }
                                ]
                            }
                        ]
                    )

                    result = response.choices[0].message.content
                    return index, file_name, result, "成功"

                except Exception as e:
                    if attempt == max_retries - 1:
                        return index, file_name, None, f"失败: {str(e)}"
                    else:
                        # 等待后重试
                        await asyncio.sleep(1)

            return index, file_name, None, "重试失败"

    async def process_images_async(self, image_files: List[str], prompt_text: str):
        """异步处理所有图片"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        tasks = []

        for i, image_path in enumerate(image_files):
            task = self.process_single_image(image_path, i, prompt_text, semaphore)
            tasks.append(task)

        # 处理任务并更新进度
        completed = 0
        total = len(tasks)

        for coro in asyncio.as_completed(tasks):
            index, file_name, result, status = await coro
            completed += 1

            # 存储结果
            self.results[index] = {
                'file_name': file_name,
                'result': result,
                'status': status
            }

            # 更新UI
            self.root.after(0, self.update_progress, completed, total, file_name, status)

    def update_progress(self, completed: int, total: int, file_name: str, status: str):
        """更新进度条和结果显示"""
        # 更新进度条
        progress = (completed / total) * 100
        self.progress_var.set(progress)

        # 更新状态
        self.status_var.set(f"处理中... {completed}/{total} - {file_name}")

        # 添加到结果树
        result_data = self.results.get(completed - 1, {})
        result_text = result_data.get('result', '')

        # 解析结果用于显示
        if result_text and status == '成功':
            data1, data2 = self.parse_ai_result(result_text)
            if data1 and data2:
                display_text = f"[{data1[:20]}{'...' if len(data1) > 20 else ''}, {data2[:20]}{'...' if len(data2) > 20 else ''}]"
            else:
                display_text = result_text[:50] + "..." if len(result_text) > 50 else result_text
        else:
            display_text = result_text[:50] + "..." if result_text and len(result_text) > 50 else result_text

        self.result_tree.insert('', 'end', values=(
            completed,
            result_data.get('file_name', ''),
            display_text,
            status
        ))

        # 如果完成了，启用导出按钮
        if completed == total:
            self.status_var.set(f"处理完成！共处理 {total} 张图片")
            self.export_btn.config(state='normal')
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            self.processing = False

    def start_recognition(self):
        """开始识别"""
        if not self.folder_path.get():
            messagebox.showerror("错误", "请选择图片文件夹")
            return

        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成")
            return

        # 获取图片文件
        image_files = self.get_image_files(self.folder_path.get())
        if not image_files:
            messagebox.showwarning("警告", "文件夹中没有找到图片文件")
            return

        # 清空之前的结果
        self.results.clear()
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 重置进度
        self.progress_var.set(0)
        self.processing = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.export_btn.config(state='disabled')

        # 获取提示文本
        prompt_text = self.get_prompt_text(self.recognition_type.get())

        # 在新线程中运行异步处理
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.process_images_async(image_files, prompt_text))
            finally:
                loop.close()

        thread = threading.Thread(target=run_async, daemon=True)
        thread.start()

    def stop_recognition(self):
        """停止识别"""
        self.processing = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("已停止")

    def parse_ai_result(self, result_text: str) -> Tuple[str, str]:
        """解析AI返回的列表格式结果"""
        if not result_text:
            return "", ""

        try:
            # 尝试使用json解析
            import json
            parsed = json.loads(result_text)
            if isinstance(parsed, list) and len(parsed) >= 2:
                return str(parsed[0]), str(parsed[1])
        except:
            pass

        try:
            # 尝试使用正则表达式解析
            import re
            # 匹配 ["内容1", "内容2"] 格式
            pattern = r'\["([^"]*)",\s*"([^"]*)"\]'
            match = re.search(pattern, result_text)
            if match:
                return match.group(1), match.group(2)

            # 匹配 ['内容1', '内容2'] 格式
            pattern = r"\['([^']*)',\s*'([^']*)'\]"
            match = re.search(pattern, result_text)
            if match:
                return match.group(1), match.group(2)
        except:
            pass

        # 如果解析失败，返回原始文本和空字符串
        return result_text, ""

    def export_to_excel(self):
        """导出结果到Excel"""
        if not self.results:
            messagebox.showwarning("警告", "没有结果可导出")
            return

        # 准备数据
        data = []
        recognition_type = self.recognition_type.get()

        for i in sorted(self.results.keys()):
            result_data = self.results[i]

            # 解析AI返回的列表数据
            if result_data['result'] and result_data['status'] == '成功':
                data1, data2 = self.parse_ai_result(result_data['result'])
            else:
                data1, data2 = "", ""

            # 根据识别类型设置列名
            if recognition_type == 1:
                row_data = {
                    '序号': i + 1,
                    '文件名': result_data['file_name'],
                    '显示屏数字/条形码编号1': data1,
                    '条形码编号/条形码编号2': data2,
                    '状态': result_data['status'],
                    '原始结果': result_data['result'] if result_data['result'] else ''
                }
            else:
                row_data = {
                    '序号': i + 1,
                    '文件名': result_data['file_name'],
                    '条形码编号': data1,
                    '资产编码': data2,
                    '状态': result_data['status'],
                    '原始结果': result_data['result'] if result_data['result'] else ''
                }

            data.append(row_data)

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 选择保存位置
        default_name = f"电表识别结果_类型{recognition_type}_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"

        file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialname=default_name
        )

        if file_path:
            try:
                df.to_excel(file_path, index=False, engine='openpyxl')
                messagebox.showinfo("成功", f"结果已导出到: {file_path}\n共导出 {len(data)} 条记录")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

def main():
    root = tk.Tk()
    ImageRecognitionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
