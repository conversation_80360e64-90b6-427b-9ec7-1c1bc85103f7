import pandas as pd
import time
import json
import re
from typing import <PERSON><PERSON>

def parse_ai_result(result_text: str) -> <PERSON><PERSON>[str, str]:
    """解析AI返回的列表格式结果"""
    if not result_text:
        return "", ""
    
    try:
        # 尝试使用json解析
        parsed = json.loads(result_text)
        if isinstance(parsed, list) and len(parsed) >= 2:
            return str(parsed[0]), str(parsed[1])
    except:
        pass
    
    try:
        # 尝试使用正则表达式解析
        # 匹配 ["内容1", "内容2"] 格式
        pattern = r'\["([^"]*)",\s*"([^"]*)"\]'
        match = re.search(pattern, result_text)
        if match:
            return match.group(1), match.group(2)
        
        # 匹配 ['内容1', '内容2'] 格式
        pattern = r"\['([^']*)',\s*'([^']*)'\]"
        match = re.search(pattern, result_text)
        if match:
            return match.group(1), match.group(2)
    except:
        pass
    
    # 如果解析失败，返回原始文本和空字符串
    return result_text, ""

def test_excel_export():
    """测试Excel导出功能"""
    print("=== Excel导出功能测试 ===")
    
    # 模拟AI返回的结果数据
    test_results = {
        0: {
            'file_name': '1.jpg',
            'result': '["06021DY0000000180343531", "06001EM01012052500055892"]',
            'status': '成功'
        },
        1: {
            'file_name': '2.jpg', 
            'result': '["电表读数12345", "条形码ABC123456"]',
            'status': '成功'
        },
        2: {
            'file_name': '3.jpg',
            'result': None,
            'status': '失败'
        },
        3: {
            'file_name': '4.jpg',
            'result': '["06021DY9999999999999999", "资产编码XYZ789"]',
            'status': '重试成功'
        }
    }
    
    # 测试两种识别类型的Excel导出
    for recognition_type in [1, 2]:
        print(f"\n--- 测试类型{recognition_type} ---")
        
        # 准备数据
        data = []
        for i in sorted(test_results.keys()):
            result_data = test_results[i]
            
            # 解析AI返回的列表数据
            if result_data['result'] and result_data['status'] == '成功':
                data1, data2 = parse_ai_result(result_data['result'])
                print(f"解析结果 {result_data['file_name']}: '{data1}' | '{data2}'")
            else:
                data1, data2 = "", ""
                print(f"解析结果 {result_data['file_name']}: 无数据（{result_data['status']}）")
            
            # 根据识别类型设置列名
            if recognition_type == 1:
                row_data = {
                    '序号': i + 1,
                    '文件名': result_data['file_name'],
                    '显示屏数字/条形码编号1': data1,
                    '条形码编号/条形码编号2': data2,
                    '状态': result_data['status'],
                    '原始结果': result_data['result'] if result_data['result'] else ''
                }
            else:
                row_data = {
                    '序号': i + 1,
                    '文件名': result_data['file_name'],
                    '条形码编号': data1,
                    '资产编码': data2,
                    '状态': result_data['status'],
                    '原始结果': result_data['result'] if result_data['result'] else ''
                }
            
            data.append(row_data)
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 保存Excel文件
        filename = f"测试_电表识别结果_类型{recognition_type}_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"
        try:
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"✅ 成功导出: {filename}")
            print(f"   共 {len(data)} 条记录")
            
            # 显示数据预览
            print("\n数据预览:")
            print(df.to_string(index=False, max_cols=6))
            
        except Exception as e:
            print(f"❌ 导出失败: {str(e)}")
    
    print("\n=== 测试完成 ===")

def test_parse_function():
    """测试解析函数"""
    print("\n=== 解析函数测试 ===")
    
    test_cases = [
        '["06021DY0000000180343531", "06001EM01012052500055892"]',
        "['电表读数12345', '条形码ABC123456']",
        '["单引号测试", "第二个数据"]',
        "['双引号测试', '第二个数据']",
        '无效格式的字符串',
        '',
        None
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case}")
        try:
            result1, result2 = parse_ai_result(test_case)
            print(f"  结果: '{result1}' | '{result2}'")
        except Exception as e:
            print(f"  错误: {str(e)}")
        print()

if __name__ == "__main__":
    test_parse_function()
    test_excel_export()
