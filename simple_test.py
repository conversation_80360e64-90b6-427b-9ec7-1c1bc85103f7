import json
import re

def parse_ai_result(result_text):
    """解析AI返回的列表格式结果"""
    if not result_text:
        return "", ""
    
    try:
        # 尝试使用json解析
        parsed = json.loads(result_text)
        if isinstance(parsed, list) and len(parsed) >= 2:
            return str(parsed[0]), str(parsed[1])
    except:
        pass
    
    try:
        # 尝试使用正则表达式解析
        # 匹配 ["内容1", "内容2"] 格式
        pattern = r'\["([^"]*)",\s*"([^"]*)"\]'
        match = re.search(pattern, result_text)
        if match:
            return match.group(1), match.group(2)
        
        # 匹配 ['内容1', '内容2'] 格式
        pattern = r"\['([^']*)',\s*'([^']*)'\]"
        match = re.search(pattern, result_text)
        if match:
            return match.group(1), match.group(2)
    except:
        pass
    
    # 如果解析失败，返回原始文本和空字符串
    return result_text, ""

# 测试解析功能
print("=== 简单解析测试 ===")

test_cases = [
    '["06021DY0000000180343531", "06001EM01012052500055892"]',
    "['电表读数12345', '条形码ABC123456']",
    '["测试数据1", "测试数据2"]',
    '无效格式'
]

for i, test_case in enumerate(test_cases, 1):
    print(f"测试 {i}: {test_case}")
    result1, result2 = parse_ai_result(test_case)
    print(f"  解析结果: '{result1}' | '{result2}'")
    print()

print("解析功能测试完成！")
