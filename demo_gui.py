import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
from pathlib import Path

class DemoApp:
    def __init__(self, root):
        self.root = root
        self.root.title("电表图片识别系统 - 演示版")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 变量
        self.folder_path = tk.StringVar(value="当前文件夹")
        self.recognition_type = tk.IntVar(value=1)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="准备就绪")
        
        self.setup_ui()
        self.load_demo_data()
    
    def setup_ui(self):
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, text="电表图片识别系统", 
                              font=('微软雅黑', 20, 'bold'), 
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="演示版 - 展示界面功能", 
                                font=('微软雅黑', 10), 
                                bg='#f0f0f0', fg='#7f8c8d')
        subtitle_label.pack()
        
        # 文件夹选择区域
        folder_frame = tk.LabelFrame(self.root, text="选择图片文件夹", 
                                   font=('微软雅黑', 12), 
                                   bg='#f0f0f0', fg='#34495e',
                                   padx=10, pady=10)
        folder_frame.pack(fill='x', padx=20, pady=10)
        
        folder_entry_frame = tk.Frame(folder_frame, bg='#f0f0f0')
        folder_entry_frame.pack(fill='x')
        
        self.folder_entry = tk.Entry(folder_entry_frame, textvariable=self.folder_path,
                                   font=('微软雅黑', 10), width=50, state='readonly')
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(folder_entry_frame, text="浏览", 
                             command=self.demo_browse,
                             font=('微软雅黑', 10),
                             bg='#3498db', fg='white',
                             relief='flat', padx=20)
        browse_btn.pack(side='right')
        
        # 识别类型选择区域
        type_frame = tk.LabelFrame(self.root, text="选择识别类型", 
                                 font=('微软雅黑', 12), 
                                 bg='#f0f0f0', fg='#34495e',
                                 padx=10, pady=10)
        type_frame.pack(fill='x', padx=20, pady=10)
        
        type1_radio = tk.Radiobutton(type_frame, text="类型1: 电表显示屏和条形码识别", 
                                   variable=self.recognition_type, value=1,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type1_radio.pack(anchor='w', pady=5)
        
        type2_radio = tk.Radiobutton(type_frame, text="类型2: 条形码和资产编码识别", 
                                   variable=self.recognition_type, value=2,
                                   font=('微软雅黑', 10), bg='#f0f0f0',
                                   activebackground='#f0f0f0')
        type2_radio.pack(anchor='w', pady=5)
        
        # 控制按钮区域
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=20, pady=20)
        
        self.start_btn = tk.Button(control_frame, text="开始识别（演示）", 
                                 command=self.demo_recognition,
                                 font=('微软雅黑', 12, 'bold'),
                                 bg='#27ae60', fg='white',
                                 relief='flat', padx=30, pady=10)
        self.start_btn.pack(side='left', padx=(0, 10))
        
        self.stop_btn = tk.Button(control_frame, text="停止", 
                                command=self.demo_stop,
                                font=('微软雅黑', 12),
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=30, pady=10,
                                state='disabled')
        self.stop_btn.pack(side='left')
        
        # 进度条区域
        progress_frame = tk.LabelFrame(self.root, text="处理进度", 
                                     font=('微软雅黑', 12), 
                                     bg='#f0f0f0', fg='#34495e',
                                     padx=10, pady=10)
        progress_frame.pack(fill='x', padx=20, pady=10)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(pady=10)
        
        self.status_label = tk.Label(progress_frame, textvariable=self.status_var,
                                   font=('微软雅黑', 10), bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.pack()
        
        # 结果显示区域
        result_frame = tk.LabelFrame(self.root, text="处理结果", 
                                   font=('微软雅黑', 12), 
                                   bg='#f0f0f0', fg='#34495e',
                                   padx=10, pady=10)
        result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 创建Treeview来显示结果
        columns = ('序号', '文件名', '识别结果', '状态')
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.result_tree.heading(col, text=col)
            if col == '序号':
                self.result_tree.column(col, width=60, anchor='center')
            elif col == '文件名':
                self.result_tree.column(col, width=200)
            elif col == '识别结果':
                self.result_tree.column(col, width=300)
            else:
                self.result_tree.column(col, width=80, anchor='center')
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)
        
        self.result_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 导出按钮
        export_frame = tk.Frame(self.root, bg='#f0f0f0')
        export_frame.pack(fill='x', padx=20, pady=10)
        
        self.export_btn = tk.Button(export_frame, text="导出到Excel（演示）", 
                                  command=self.demo_export,
                                  font=('微软雅黑', 10),
                                  bg='#f39c12', fg='white',
                                  relief='flat', padx=20, pady=5,
                                  state='disabled')
        self.export_btn.pack(side='right')
    
    def load_demo_data(self):
        """加载演示数据"""
        # 获取当前目录的图片文件
        current_dir = "."
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        image_files = []
        
        try:
            for file_path in Path(current_dir).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    image_files.append(file_path.name)
        except:
            image_files = ["1.jpg", "2.jpg", "3.jpg", "4.jpg"]  # 默认文件
        
        image_files.sort()
        self.demo_files = image_files
    
    def demo_browse(self):
        """演示浏览功能"""
        messagebox.showinfo("演示", f"当前文件夹包含 {len(self.demo_files)} 个图片文件:\n" + 
                           "\n".join(self.demo_files))
    
    def demo_recognition(self):
        """演示识别功能"""
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.export_btn.config(state='disabled')
        
        # 清空结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 开始演示处理
        def process_demo():
            for i, filename in enumerate(self.demo_files):
                if not hasattr(self, 'demo_running') or not self.demo_running:
                    break
                
                # 模拟处理时间
                time.sleep(0.5)
                
                # 更新进度
                progress = ((i + 1) / len(self.demo_files)) * 100
                self.root.after(0, self.update_demo_progress, i + 1, filename, progress)
            
            # 完成处理
            self.root.after(0, self.demo_complete)
        
        self.demo_running = True
        thread = threading.Thread(target=process_demo, daemon=True)
        thread.start()
    
    def update_demo_progress(self, index, filename, progress):
        """更新演示进度"""
        self.progress_var.set(progress)
        self.status_var.set(f"处理中... {index}/{len(self.demo_files)} - {filename}")
        
        # 生成模拟结果
        if self.recognition_type.get() == 1:
            result = f'["电表读数_{index:03d}", "条形码_{index:06d}"]'
        else:
            result = f'["06021DY{index:010d}", "06001EM{index:011d}"]'
        
        status = "成功" if index % 4 != 0 else "重试成功"
        
        # 添加到结果树
        self.result_tree.insert('', 'end', values=(index, filename, result, status))
    
    def demo_complete(self):
        """演示完成"""
        self.status_var.set(f"处理完成！共处理 {len(self.demo_files)} 张图片")
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.export_btn.config(state='normal')
        self.demo_running = False
    
    def demo_stop(self):
        """演示停止"""
        self.demo_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("已停止")
    
    def demo_export(self):
        """演示导出功能"""
        recognition_type = self.recognition_type.get()
        filename = f"电表识别结果_类型{recognition_type}_演示.xlsx"
        messagebox.showinfo("演示", f"演示导出功能\n将保存为: {filename}\n\n实际版本会打开文件保存对话框")

def main():
    root = tk.Tk()
    DemoApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
